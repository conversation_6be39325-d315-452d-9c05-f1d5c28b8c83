{"name": "@electrical/backend", "version": "1.0.0", "description": "Backend API for electrical contracting application", "main": "dist/index.js", "scripts": {"dev": "node dist/index.js", "start": "node dist/index.js", "build": "tsc", "test": "jest", "lint": "eslint src --ext .ts", "typecheck": "tsc --noEmit"}, "dependencies": {"@prisma/client": "^5.8.1", "axios": "^1.6.5", "bcryptjs": "^2.4.3", "bullmq": "^5.1.5", "compression": "^1.7.4", "cors": "^2.8.5", "csv-parser": "^3.0.0", "date-fns": "^3.3.1", "decimal.js": "^10.4.3", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "ioredis": "^5.3.2", "json2csv": "^6.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "pdf-lib": "^1.17.1", "prisma": "^5.8.1", "qrcode": "^1.5.3", "socket.io": "^4.7.4", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/csv-parser": "^3.0.0", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.11.5", "@types/qrcode": "^1.5.5", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "eslint": "^8.56.0", "jest": "^29.7.0", "typescript": "^5.3.3"}, "keywords": [], "author": "", "license": "ISC"}