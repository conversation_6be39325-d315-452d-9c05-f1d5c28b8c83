import { Worker } from 'bullmq';
import Redis from 'ioredis';
import { config } from '../config';
import { materialPriceWorker } from './material-price';
import { calculationWorker } from './calculation';
import { logger } from '../index';

export function startWorkers(): void {
  // Create a separate Redis connection for BullMQ with correct configuration
  const bullmqRedis = new Redis({
    host: config.redis.host,
    port: config.redis.port,
    password: config.redis.password,
    maxRetriesPerRequest: null,
    enableOfflineQueue: false,
    retryStrategy: () => null
  });
  
  // Material price update worker
  const priceWorker = new Worker(
    'material-prices',
    materialPriceWorker,
    {
      connection: bullmqRedis,
      concurrency: 5,
      removeOnComplete: { count: 100 },
      removeOnFail: { count: 50 }
    }
  );
  
  priceWorker.on('completed', (job) => {
    logger.info(`Material price job ${job.id} completed`);
  });
  
  priceWorker.on('failed', (job, err) => {
    logger.error(`Material price job ${job?.id} failed:`, err);
  });
  
  // Complex calculation worker
  const calcWorker = new Worker(
    'calculations',
    calculationWorker,
    {
      connection: bullmqRedis,
      concurrency: 3,
      removeOnComplete: { count: 50 },
      removeOnFail: { count: 25 }
    }
  );
  
  calcWorker.on('completed', (job) => {
    logger.info(`Calculation job ${job.id} completed`);
  });
  
  calcWorker.on('failed', (job, err) => {
    logger.error(`Calculation job ${job?.id} failed:`, err);
  });
  
  logger.info('Background workers started');
}