{"name": "electrical-contracting-saas", "version": "1.0.0", "description": "Electrical contracting application with AI agents", "private": true, "workspaces": ["frontend", "backend", "shared", "agents"], "scripts": {"dev": "pnpm run --parallel dev", "dev:backend": "pnpm --filter @electrical/backend dev", "dev:frontend": "pnpm --filter @electrical/frontend dev", "dev:main": "concurrently \"pnpm --filter @electrical/backend dev\" \"pnpm --filter @electrical/frontend dev\"", "dev:simple": "pnpm --filter @electrical/backend dev & pnpm --filter @electrical/frontend dev", "build": "pnpm run -r build", "test": "pnpm run -r test", "lint": "pnpm run -r lint", "typecheck": "pnpm run -r typecheck"}, "devDependencies": {"@types/node": "^20.11.5", "typescript": "^5.3.3"}, "engines": {"node": ">=20.0.0", "pnpm": ">=8.0.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}