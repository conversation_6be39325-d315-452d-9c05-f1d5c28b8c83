generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id                 String           @id @default(uuid())
  email              String           @unique
  password_hash      String
  name               String
  role               String           @default("electrician")
  refresh_token      String?
  created_at         DateTime         @default(now())
  updated_at         DateTime         @updatedAt
  deleted_at         DateTime?
  calculations       CalculationLog[]
  estimates_approved Estimate[]       @relation("EstimateApprover")
  estimates_created  Estimate[]       @relation("EstimateCreator")

  @@index([email])
  @@index([deleted_at])
}

model Customer {
  id               String    @id @default(uuid())
  name             String
  email            String?
  phone            String?
  address          String?
  city             String?
  state            String?
  zip              String?
  license_number   String?
  insurance_expiry DateTime?
  credit_limit     Float?
  payment_terms    String    @default("NET30")
  created_at       DateTime  @default(now())
  updated_at       DateTime  @updatedAt
  deleted_at       DateTime?
  projects         Project[]

  @@index([name])
  @@index([deleted_at])
}

model Project {
  id                    String                @id @default(uuid())
  customer_id           String
  name                  String
  address               String
  city                  String
  state                 String
  zip                   String
  type                  String
  status                String
  voltage_system        String
  service_size          Int
  square_footage        Int?
  permit_number         String?
  permit_expiry         DateTime?
  inspection_status     String?
  panel_count           Int?
  main_disconnect_type  String?
  grounding_system      String?
  has_generator         Boolean               @default(false)
  has_solar             Boolean               @default(false)
  created_at            DateTime              @default(now())
  updated_at            DateTime              @updatedAt
  calculations          CalculationLog[]
  estimates             Estimate[]
  inspection_checklists InspectionChecklist[]
  panels                Panel[]
  permit_documents      PermitDocument[]
  customer              Customer              @relation(fields: [customer_id], references: [id])

  @@index([customer_id])
  @@index([status])
  @@index([type])
}

model Estimate {
  id                  String         @id @default(uuid())
  project_id          String
  version             Int            @default(1)
  status              String
  valid_until         DateTime
  subtotal            Float          @default(0)
  tax_total           Float          @default(0)
  total_amount        Float          @default(0)
  profit_margin       Float          @default(15)
  contingency_percent Float          @default(10)
  notes               String?
  terms               String?
  created_by          String
  approved_by         String?
  approved_at         DateTime?
  created_at          DateTime       @default(now())
  updated_at          DateTime       @updatedAt
  approver            User?          @relation("EstimateApprover", fields: [approved_by], references: [id])
  creator             User           @relation("EstimateCreator", fields: [created_by], references: [id])
  project             Project        @relation(fields: [project_id], references: [id])
  labor_items         LaborItem[]
  material_items      MaterialItem[]

  @@unique([project_id, version])
  @@index([project_id])
  @@index([status])
}

model MaterialItem {
  id              String   @id @default(uuid())
  estimate_id     String
  catalog_number  String
  description     String
  category        String
  unit            String
  quantity        Float
  unit_cost       Float
  markup_percent  Float    @default(35)
  waste_percent   Float    @default(5)
  tax_rate        Float    @default(0.0875)
  extended_cost   Float    @default(0)
  tax_amount      Float    @default(0)
  total_amount    Float    @default(0)
  supplier        String?
  wire_size       String?
  wire_type       String?
  conduit_size    String?
  conduit_type    String?
  voltage_rating  Int?
  amperage_rating Int?
  phase           String?
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  estimate        Estimate @relation(fields: [estimate_id], references: [id])

  @@index([estimate_id])
  @@index([catalog_number])
  @@index([category])
}

model LaborItem {
  id             String   @id @default(uuid())
  estimate_id    String
  description    String
  trade          String
  hours          Float
  rate           Float
  overtime_hours Float    @default(0)
  overtime_rate  Float
  burden_percent Float    @default(35)
  extended_cost  Float    @default(0)
  burden_amount  Float    @default(0)
  total_amount   Float    @default(0)
  created_at     DateTime @default(now())
  updated_at     DateTime @updatedAt
  estimate       Estimate @relation(fields: [estimate_id], references: [id])

  @@index([estimate_id])
  @@index([trade])
}

model CalculationLog {
  id               String   @id @default(uuid())
  calculation_type String
  input_data       String
  output_data      String
  nec_references   String
  performed_by     String
  project_id       String?
  created_at       DateTime @default(now())
  user             User     @relation(fields: [performed_by], references: [id])
  project          Project? @relation(fields: [project_id], references: [id])

  @@index([calculation_type])
  @@index([project_id])
  @@index([performed_by])
}

model MaterialPriceHistory {
  id             String   @id @default(uuid())
  catalog_number String
  supplier       String
  unit_cost      Float
  effective_date DateTime
  created_at     DateTime @default(now())

  @@index([catalog_number, supplier])
  @@index([effective_date])
}

model Panel {
  id                         String                    @id @default(uuid())
  project_id                 String
  name                       String
  location                   String
  panel_type                 String
  manufacturer               String?
  model_number               String?
  catalog_number             String?
  voltage_system             String
  ampere_rating              Int
  bus_rating                 Int
  main_breaker_size          Int?
  phase_config               String
  mounting_type              String
  enclosure_type             String
  spaces_total               Int
  spaces_used                Int                       @default(0)
  fed_from_panel_id          String?
  fed_from_circuit           Int?
  notes                      String?
  created_at                 DateTime                  @default(now())
  updated_at                 DateTime                  @updatedAt
  arc_flash_calculations     ArcFlashCalculation[]
  circuits                   Circuit[]
  fed_from_panel             Panel?                    @relation("PanelFeeding", fields: [fed_from_panel_id], references: [id])
  feeding_panels             Panel[]                   @relation("PanelFeeding")
  project                    Project                   @relation(fields: [project_id], references: [id])
  load_calculations          PanelLoadCalculation[]
  short_circuit_calculations ShortCircuitCalculation[]

  @@index([project_id])
  @@index([fed_from_panel_id])
}

model Circuit {
  id               String   @id @default(uuid())
  panel_id         String
  circuit_number   Int
  description      String
  breaker_size     Int
  breaker_type     String
  poles            Int
  phase_connection String?
  wire_size        String
  wire_type        String
  wire_count       Int
  conduit_type     String?
  conduit_size     String?
  voltage          Int
  load_type        String
  continuous_load  Boolean  @default(false)
  connected_load   Float
  demand_factor    Float    @default(1.0)
  calculated_load  Float
  room_area        String?
  control_type     String?
  is_spare         Boolean  @default(false)
  is_space         Boolean  @default(false)
  notes            String?
  created_at       DateTime @default(now())
  updated_at       DateTime @updatedAt
  panel            Panel    @relation(fields: [panel_id], references: [id])

  @@unique([panel_id, circuit_number])
  @@index([panel_id])
  @@index([load_type])
}

model PanelLoadCalculation {
  id                      String   @id @default(uuid())
  panel_id                String
  calculation_date        DateTime @default(now())
  phase_a_load            Float    @default(0)
  phase_b_load            Float    @default(0)
  phase_c_load            Float    @default(0)
  neutral_load            Float    @default(0)
  total_connected_load    Float    @default(0)
  total_demand_load       Float    @default(0)
  load_percentage         Float    @default(0)
  phase_imbalance_percent Float    @default(0)
  power_factor            Float    @default(0.9)
  ambient_temperature     Float    @default(30)
  derating_factor         Float    @default(1.0)
  notes                   String?
  created_by              String
  panel                   Panel    @relation(fields: [panel_id], references: [id])

  @@index([panel_id])
  @@index([calculation_date])
}

model BreakerType {
  id               String   @id @default(uuid())
  manufacturer     String
  series           String
  catalog_number   String
  ampere_rating    Int
  poles            Int
  voltage_rating   Int
  interrupt_rating Int
  breaker_type     String
  width_inches     Float
  list_price       Float?
  created_at       DateTime @default(now())
  updated_at       DateTime @updatedAt

  @@unique([manufacturer, catalog_number])
  @@index([manufacturer, series])
}

model NecReference {
  id          String   @id @default(uuid())
  nec_edition String
  article     String
  section     String
  title       String
  description String
  category    String
  created_at  DateTime @default(now())

  @@index([nec_edition, article])
  @@index([category])
}

model Material {
  id              String    @id @default(uuid())
  sku             String    @unique
  name            String
  description     String?
  category        String
  manufacturer    String
  model_number    String?
  unit            String
  current_price   Float?
  price_updated   DateTime?
  in_stock        Boolean   @default(true)
  min_quantity    Int       @default(1)
  created_at      DateTime  @default(now())
  updated_at      DateTime  @updatedAt
  wire_size       String?
  wire_type       String?
  conduit_size    String?
  conduit_type    String?
  voltage_rating  Int?
  amperage_rating Int?
  phase           String?
  color           String?

  @@index([category])
  @@index([manufacturer])
}

model ArcFlashCalculation {
  id                             String   @id @default(uuid())
  panel_id                       String
  calculation_method             String   @default("IEEE_1584_2018")
  system_voltage                 Float
  system_type                    String
  grounding_type                 String
  frequency                      Int      @default(60)
  bolted_fault_current           Float
  fault_clearing_time            Float
  equipment_type                 String
  electrode_configuration        String
  enclosure_width                Float
  enclosure_height               Float
  enclosure_depth                Float
  conductor_gap                  Float
  working_distance               Float
  arcing_current_min             Float
  arcing_current_max             Float
  incident_energy_min            Float
  incident_energy_max            Float
  arc_flash_boundary             Float
  ppe_category                   Int
  ppe_min_arc_rating             Float
  shock_hazard_voltage           Float
  limited_approach               Float
  restricted_approach            Float
  enclosure_correction           Float    @default(1.0)
  arc_duration_max               Float    @default(2.0)
  reduced_arcing_current         Float?
  reduced_incident_energy        Float?
  hazard_risk_category           Int
  requires_energized_work_permit Boolean  @default(true)
  recommended_ppe                String
  safety_notes                   String?
  calculated_by                  String
  reviewed_by                    String?
  calculation_date               DateTime @default(now())
  valid_until                    DateTime
  report_generated               Boolean  @default(false)
  report_path                    String?
  created_at                     DateTime @default(now())
  updated_at                     DateTime @updatedAt
  panel                          Panel    @relation(fields: [panel_id], references: [id])

  @@index([panel_id])
  @@index([calculation_date])
  @@index([ppe_category])
}

model ShortCircuitCalculation {
  id                            String   @id @default(uuid())
  panel_id                      String
  calculation_method            String   @default("POINT_TO_POINT")
  utility_voltage               Float
  utility_fault_current         Float
  utility_x_r_ratio             Float    @default(10.0)
  transformer_id                String?
  transformer_kva               Float?
  transformer_impedance         Float?
  transformer_x_r_ratio         Float?
  transformer_primary_v         Float?
  transformer_secondary_v       Float?
  transformer_type              String?
  conductor_length              Float
  conductor_size                String
  conductor_material            String   @default("COPPER")
  conductor_type                String
  conductors_per_phase          Int      @default(1)
  conduit_type                  String
  motor_contribution            Boolean  @default(false)
  motor_hp_total                Float?
  motor_contribution_multiplier Float    @default(4.0)
  source_impedance_r            Float
  source_impedance_x            Float
  transformer_impedance_r       Float?
  transformer_impedance_x       Float?
  conductor_impedance_r         Float
  conductor_impedance_x         Float
  total_impedance_r             Float
  total_impedance_x             Float
  total_impedance_z             Float
  total_x_r_ratio               Float
  symmetrical_fault_3ph         Float
  symmetrical_fault_lg          Float
  symmetrical_fault_ll          Float
  asymmetrical_fault_3ph        Float
  peak_fault_current            Float
  bus_bracing_adequate          Boolean
  bus_bracing_rating            Float?
  main_breaker_adequate         Boolean
  main_breaker_aic              Float?
  branch_breaker_adequate       Boolean
  branch_breaker_aic            Float?
  series_rated                  Boolean  @default(false)
  upstream_device               String?
  downstream_device             String?
  series_rating_aic             Float?
  device_time_bands             String?
  coordination_verified         Boolean  @default(false)
  coordination_notes            String?
  safety_factor                 Float    @default(1.0)
  calculation_notes             String?
  assumptions                   String?
  calculated_by                 String
  reviewed_by                   String?
  calculation_date              DateTime @default(now())
  valid_until                   DateTime
  report_generated              Boolean  @default(false)
  report_path                   String?
  created_at                    DateTime @default(now())
  updated_at                    DateTime @updatedAt
  panel                         Panel    @relation(fields: [panel_id], references: [id])

  @@index([panel_id])
  @@index([calculation_date])
  @@index([calculation_method])
}

model PermitDocument {
  id                    String                @id @default(uuid())
  project_id            String
  document_type         String
  document_version      Int                   @default(1)
  jurisdiction          String
  jurisdiction_code     String?
  title                 String
  description           String?
  template_id           String?
  status                String                @default("DRAFT")
  permit_number         String?
  submission_date       DateTime?
  approval_date         DateTime?
  expiration_date       DateTime?
  rejection_reason      String?
  inspector_notes       String?
  form_data             String
  included_calculations String?
  included_panels       String?
  attachments           String?
  signature_required    Boolean               @default(false)
  signed_by             String?
  signature_date        DateTime?
  signature_data        String?
  generated_pdf_path    String?
  generation_date       DateTime?
  file_size_bytes       Int?
  page_count            Int?
  created_by            String
  reviewed_by           String?
  created_at            DateTime              @default(now())
  updated_at            DateTime              @updatedAt
  inspection_checklists InspectionChecklist[]
  project               Project               @relation(fields: [project_id], references: [id])

  @@unique([project_id, document_type, document_version])
  @@index([project_id])
  @@index([status])
  @@index([jurisdiction])
  @@index([submission_date])
}

model JurisdictionTemplate {
  id                 String    @id @default(uuid())
  jurisdiction_name  String
  jurisdiction_code  String    @unique
  state              String
  document_type      String
  template_name      String
  template_version   String
  effective_date     DateTime
  expiration_date    DateTime?
  form_fields        String
  required_documents String
  fee_schedule       String?
  nec_edition        String    @default("2023")
  local_amendments   String?
  department_name    String?
  department_phone   String?
  department_email   String?
  submission_url     String?
  created_at         DateTime  @default(now())
  updated_at         DateTime  @updatedAt

  @@unique([jurisdiction_code, document_type])
  @@index([state])
  @@index([document_type])
}

model InspectionChecklist {
  id                    String                    @id @default(uuid())
  project_id            String
  permit_document_id    String?
  inspection_type       String
  inspection_subtype    String?
  inspection_number     Int                       @default(1)
  inspection_date       DateTime?
  scheduled_date        DateTime?
  inspector_name        String?
  inspector_id          String?
  inspector_company     String?
  inspector_phone       String?
  inspector_email       String?
  status                String                    @default("PENDING")
  overall_result        String?
  reinspection_required Boolean                   @default(false)
  corrections_required  Boolean                   @default(false)
  location_details      String?
  access_instructions   String?
  qr_code_id            String?                   @unique
  qr_code_url           String?
  contractor_present    Boolean                   @default(false)
  contractor_name       String?
  contractor_signature  String?
  inspector_signature   String?
  sign_off_date         DateTime?
  report_generated      Boolean                   @default(false)
  report_path           String?
  report_sent_date      DateTime?
  created_by            String
  created_at            DateTime                  @default(now())
  updated_at            DateTime                  @updatedAt
  completed_at          DateTime?
  permit_document       PermitDocument?           @relation(fields: [permit_document_id], references: [id])
  project               Project                   @relation(fields: [project_id], references: [id])
  checklist_items       InspectionChecklistItem[]
  photos                InspectionPhoto[]

  @@index([project_id])
  @@index([inspection_type])
  @@index([status])
  @@index([inspection_date])
  @@index([qr_code_id])
}

model InspectionChecklistItem {
  id                     String              @id @default(uuid())
  checklist_id           String
  category               String
  item_code              String
  description            String
  nec_reference          String?
  inspection_criteria    String?
  common_failures        String?
  status                 String              @default("NOT_INSPECTED")
  inspector_notes        String?
  failure_reason         String?
  correction_required    Boolean             @default(false)
  correction_description String?
  correction_deadline    DateTime?
  correction_completed   Boolean             @default(false)
  correction_date        DateTime?
  correction_verified_by String?
  photo_required         Boolean             @default(false)
  photos_attached        Int                 @default(0)
  severity               String              @default("STANDARD")
  is_code_violation      Boolean             @default(false)
  immediate_danger       Boolean             @default(false)
  measurement_required   Boolean             @default(false)
  measurement_type       String?
  measurement_value      String?
  measurement_unit       String?
  measurement_range_min  Float?
  measurement_range_max  Float?
  sequence_number        Int                 @default(0)
  is_required            Boolean             @default(true)
  created_at             DateTime            @default(now())
  updated_at             DateTime            @updatedAt
  inspected_at           DateTime?
  checklist              InspectionChecklist @relation(fields: [checklist_id], references: [id])

  @@unique([checklist_id, item_code])
  @@index([checklist_id])
  @@index([category])
  @@index([status])
  @@index([correction_required])
}

model InspectionPhoto {
  id              String              @id @default(uuid())
  checklist_id    String
  item_id         String?
  file_path       String
  file_name       String
  file_size       Int
  mime_type       String              @default("image/jpeg")
  caption         String?
  photo_type      String
  location_tag    String?
  latitude        Float?
  longitude       Float?
  thumbnail_path  String?
  is_annotated    Boolean             @default(false)
  annotation_data String?
  uploaded_by     String
  uploaded_at     DateTime            @default(now())
  checklist       InspectionChecklist @relation(fields: [checklist_id], references: [id])

  @@index([checklist_id])
  @@index([item_id])
  @@index([photo_type])
}

model InspectionTemplate {
  id              String   @id @default(uuid())
  template_name   String
  inspection_type String
  nec_edition     String   @default("2023")
  jurisdiction    String?
  description     String?
  checklist_items String
  required_photos String?
  common_issues   String?
  is_active       Boolean  @default(true)
  is_default      Boolean  @default(false)
  category_order  String?
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  @@unique([inspection_type, jurisdiction, nec_edition])
  @@index([inspection_type])
  @@index([is_active])
}
